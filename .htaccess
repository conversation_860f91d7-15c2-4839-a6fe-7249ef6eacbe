# 95目录网 - Apache配置文件
# 支持SVG图片显示和其他MIME类型配置

# ==========================================
# MIME类型配置
# ==========================================

# SVG图片支持 - 增强配置
AddType image/svg+xml .svg
AddType image/svg+xml .svgz

# 强制SVG MIME类型 (解决某些服务器配置问题)
<FilesMatch "\.svg$">
    ForceType image/svg+xml
    Header set Content-Type "image/svg+xml; charset=utf-8"
</FilesMatch>

<FilesMatch "\.svgz$">
    ForceType image/svg+xml
    Header set Content-Type "image/svg+xml; charset=utf-8"
    Header set Content-Encoding gzip
</FilesMatch>

# 其他图片格式
AddType image/webp .webp
AddType image/avif .avif

# 字体文件
AddType font/woff .woff
AddType font/woff2 .woff2
AddType application/font-woff .woff
AddType application/font-woff2 .woff2
AddType application/vnd.ms-fontobject .eot
AddType font/truetype .ttf
AddType font/opentype .otf

# 视频文件
AddType video/mp4 .mp4
AddType video/webm .webm
AddType video/ogg .ogv

# 音频文件
AddType audio/mp3 .mp3
AddType audio/ogg .ogg
AddType audio/wav .wav

# 文档文件
AddType application/pdf .pdf
AddType application/json .json

# ==========================================
# 安全配置
# ==========================================

# 防止访问敏感文件
<Files "config.php">
    Order Allow,Deny
    Deny from all
</Files>

<Files "*.log">
    Order Allow,Deny
    Deny from all
</Files>

# 防止访问备份文件
<FilesMatch "\.(bak|backup|old|tmp)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# ==========================================
# 性能优化
# ==========================================

# 启用压缩
<IfModule mod_deflate.c>
    # 压缩文本文件
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
    
    # 压缩SVG
    AddOutputFilterByType DEFLATE image/svg+xml
</IfModule>

# 缓存配置
<IfModule mod_expires.c>
    ExpiresActive On
    
    # 图片缓存1个月
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
    ExpiresByType image/webp "access plus 1 month"
    
    # CSS和JS缓存1周
    ExpiresByType text/css "access plus 1 week"
    ExpiresByType application/javascript "access plus 1 week"
    ExpiresByType application/x-javascript "access plus 1 week"
    
    # 字体缓存1年
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
    ExpiresByType application/font-woff "access plus 1 year"
    ExpiresByType application/font-woff2 "access plus 1 year"
</IfModule>

# ==========================================
# URL重写规则
# ==========================================

RewriteEngine On

# 首页、分类浏览、数据归档、最近更新、排行榜、意见反馈
RewriteRule ^(index|webdir|weblink|article|category|update|archives|top|feedback|link|rssfeed|sitemap)/?$ index.php?mod=$1 [L]

# 最近更新
RewriteRule ^update/(\d+)\.html$ index.php?mod=update&days=$1 [L]
RewriteRule ^update/(\d+)-(\d+)\.html$ index.php?mod=update&days=$1&page=$2 [L]

# 数据归档
RewriteRule ^archives/(\d+)\.html$ index.php?mod=archives&date=$1 [L]
RewriteRule ^archives/(\d+)-(\d+)\.html$ index.php?mod=archives&date=$1&page=$2 [L]

# 站内搜索
RewriteRule ^search/(name|url|tags|intro|article)/(.+)-(\d+)\.html$ index.php?mod=search&type=$1&query=$2&page=$3 [L]
RewriteRule ^search/(name|url|tags|intro|article)/(.+)\.html$ index.php?mod=search&type=$1&query=$2 [L]

# 站点详细 - SEO友好URL（包含网站名称和域名）
RewriteRule ^siteinfo/(\d+)-([^-]+)-([^.]+)\.html$ index.php?mod=siteinfo&wid=$1 [L]
# 站点详细 - 兼容原有URL格式
RewriteRule ^siteinfo/(\d+)\.html$ index.php?mod=siteinfo&wid=$1 [L]

# 文章详细
RewriteRule ^artinfo/(\d+)\.html$ index.php?mod=artinfo&aid=$1 [L]

# 链接详细
RewriteRule ^linkinfo/(\d+)\.html$ index.php?mod=linkinfo&lid=$1 [L]

# 单页
RewriteRule ^diypage/(\d+)\.html$ index.php?mod=diypage&pid=$1 [L]

# RSS
RewriteRule ^rssfeed/(\d+)\.html$ index.php?mod=rssfeed&cid=$1 [L]

# SiteMap
RewriteRule ^sitemap/(\d+)\.html$ index.php?mod=sitemap&cid=$1 [L]

# 分类目录
RewriteRule ^webdir/(.+)/(\d+)\.html$ index.php?mod=webdir&cid=$2 [L]
RewriteRule ^webdir/(.+)/(\d+)-(\d+)\.html$ index.php?mod=webdir&cid=$2&page=$3 [L]
RewriteRule ^article/(.+)/(\d+)\.html$ index.php?mod=article&cid=$2 [L]
RewriteRule ^article/(.+)/(\d+)-(\d+)\.html$ index.php?mod=article&cid=$2&page=$3 [L]

# ==========================================
# 错误页面
# ==========================================

ErrorDocument 404 /404.html
